import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { motion } from "framer-motion";
import {
  Search,
  ArrowLeft,
  Eye,
  Users,
  Filter,
  Download,
} from "lucide-react";
import { ViewAssignmentDialog } from "@/components/ViewAssignmentDialog";

// Training data structure
const trainingAreas = ["Clinical", "Administrative", "Technical"];

const trainingTopics = {
  Clinical: ["Patient Care", "Emergency Medicine", "Pharmacy", "Nursing"],
  Administrative: ["HR Management", "Finance", "Operations", "Quality Control"],
  Technical: [
    "IT Systems",
    "Medical Equipment",
    "Facilities",
    "Data Management",
  ],
};

const trainingUnits = {
  "Patient Care": [
    "Basic Patient Care",
    "Advanced Patient Care",
    "Critical Care",
  ],
  "Emergency Medicine": ["Triage", "Emergency Response", "Trauma Management"],
  Pharmacy: [
    "Medication Management",
    "Pharmacy Operations",
    "Drug Interactions",
  ],
  Nursing: ["Basic Nursing", "Advanced Nursing", "Specialized Nursing"],
  "HR Management": [
    "Recruitment",
    "Employee Relations",
    "Performance Management",
  ],
  Finance: ["Budgeting", "Financial Reporting", "Cost Control"],
  Operations: [
    "Workflow Optimization",
    "Resource Management",
    "Process Improvement",
  ],
  "Quality Control": ["Quality Assurance", "Compliance", "Risk Management"],
  "IT Systems": ["EHR Systems", "Network Management", "Cybersecurity"],
  "Medical Equipment": [
    "Equipment Operation",
    "Maintenance",
    "Troubleshooting",
  ],
  Facilities: [
    "Facility Management",
    "Safety Protocols",
    "Environmental Services",
  ],
  "Data Management": ["Data Entry", "Data Analysis", "Reporting"],
};

// Generate table data
const generateTrainingTableData = () => {
  const data: Array<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    unit?: string;
    isMainTopic: boolean;
  }> = [];

  Object.entries(trainingTopics).forEach(([area, topics]) => {
    topics.forEach((topic, topicIndex) => {
      const units = trainingUnits[topic] || [];

      // Add only main topic row with units as a comma-separated string
      data.push({
        area,
        topic,
        topicId: `T-${area.substring(0, 3).toUpperCase()}-${String(topicIndex + 1).padStart(3, '0')}`,
        version: "v1.0",
        responsibleDept: area === "Clinical" ? "Clinical Services" :
                        area === "Administrative" ? "Management" : "IT Services",
        contentOwner: `${topic} Team`,
        units: units.join(", "), // Add units as comma-separated string
        isMainTopic: true,
      });
    });
  });

  return data;
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export default function TrainingLibraryAssignment() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedArea, setSelectedArea] = useState("");
  const [viewAssignmentOpen, setViewAssignmentOpen] = useState(false);
  const [selectedAssignmentItem, setSelectedAssignmentItem] = useState<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    units?: string;
  } | null>(null);

  const tableData = generateTrainingTableData();

  // Filter data based on search term and selected area
  const filteredData = tableData.filter((item) => {
    const matchesSearch = searchTerm === "" ||
      item.area.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.topic.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.units && item.units.toLowerCase().includes(searchTerm.toLowerCase())) ||
      item.topicId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.responsibleDept.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.contentOwner.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesArea = selectedArea === "" || item.area === selectedArea;

    return matchesSearch && matchesArea;
  });

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/employee-training-assignment')}
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Back to Employee Training Assignment
          </Button>
        </div>
        
        <h1 className="text-3xl font-bold tracking-tight mb-2">
          Training Library & Assignment
        </h1>
        <p className="text-muted-foreground">
          Browse and assign training content from the library with area, topic, and unit hierarchy
        </p>
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="shadow-sm border-slate-200">
          <div className="p-6">
            {/* Search and Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search areas, topics, units, or IDs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={selectedArea}
                onChange={(e) => setSelectedArea(e.target.value)}
                className="px-3 py-2 border border-input bg-background rounded-md text-sm"
              >
                <option value="">All Areas</option>
                {trainingAreas.map((area) => (
                  <option key={area} value={area}>
                    {area}
                  </option>
                ))}
              </select>

              <Button variant="outline" className="gap-2">
                <Download size={16} />
                Export
              </Button>
            </div>

            {/* Results Summary */}
            <div className="mb-4 text-sm text-muted-foreground">
              Showing {filteredData.length} of {tableData.length} training items
              {searchTerm && ` for "${searchTerm}"`}
              {selectedArea && ` in ${selectedArea}`}
            </div>

            {/* Training Library Table */}
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader className="bg-slate-50">
                  <TableRow>
                    <TableHead className="font-medium">Area</TableHead>
                    <TableHead className="font-medium">Topic</TableHead>
                    <TableHead className="font-medium">Units</TableHead>
                    <TableHead className="font-medium">Topic ID</TableHead>
                    <TableHead className="font-medium">Version</TableHead>
                    <TableHead className="font-medium">Responsible Dept.</TableHead>
                    <TableHead className="font-medium">Content Owner</TableHead>
                    <TableHead className="font-medium text-center">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.length > 0 ? (
                    filteredData.map((item, index) => (
                      <TableRow
                        key={index}
                        className="hover:bg-muted/50"
                      >
                        <TableCell className="font-medium">{item.area}</TableCell>
                        <TableCell>
                          <span className="font-semibold text-primary">{item.topic}</span>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {item.units || "No units"}
                        </TableCell>
                        <TableCell className="font-mono text-sm">{item.topicId}</TableCell>
                        <TableCell>{item.version}</TableCell>
                        <TableCell>{item.responsibleDept}</TableCell>
                        <TableCell>{item.contentOwner}</TableCell>
                        <TableCell>
                          <div className="flex gap-2 justify-center">
                            <Button
                              size="sm"
                              className="gap-1 bg-purple-600 hover:bg-purple-700"
                              onClick={() => {
                                // Handle assign/passage functionality
                                console.log("Assign/Passage clicked for:", item);
                              }}
                            >
                              <Users size={14} />
                              Assign/Passage
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="gap-1"
                              onClick={() => {
                                setSelectedAssignmentItem(item);
                                setViewAssignmentOpen(true);
                              }}
                            >
                              <Eye size={14} />
                              View Assignment
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <Filter className="h-8 w-8 mb-2 opacity-40" />
                          <p className="text-sm font-medium">No training content found</p>
                          <p className="text-xs mt-1">
                            Try adjusting your search or filter criteria
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* View Assignment Dialog */}
      <ViewAssignmentDialog
        open={viewAssignmentOpen}
        onOpenChange={setViewAssignmentOpen}
        assignmentItem={selectedAssignmentItem}
      />
    </motion.div>
  );
}
