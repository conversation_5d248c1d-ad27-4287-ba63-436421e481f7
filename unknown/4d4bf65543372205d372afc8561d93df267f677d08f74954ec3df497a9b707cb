import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { motion } from "framer-motion";
import {
  Search,
  ArrowLeft,
  Eye,
  Users,
  Filter,
  Download,
  CheckSquare,
} from "lucide-react";
import { ViewAssignmentDialog } from "@/components/ViewAssignmentDialog";

// Checklist data structure - focused on checklists from CFD hierarchy
const checklistAreas = [
  "Patient Safety Checklist",
  "Medication Checklist", 
  "Equipment Checklist",
];

const checklistTopics = {
  "Patient Safety Checklist": [
    "Patient Safety Checklist",
  ],
  "Medication Checklist": [
    "Medication Checklist",
  ],
  "Equipment Checklist": [
    "Equipment Checklist",
  ],
};

const checklistUnits = {
  "Patient Safety Checklist": [
    "Patient Identification Verification",
    "Allergy Check",
    "Fall Risk Assessment",
    "Infection Control Measures",
    "Emergency Equipment Check",
    "Vital Signs Monitoring",
    "Pain Assessment",
    "Discharge Planning"
  ],
  "Medication Checklist": [
    "Medication Reconciliation",
    "Dosage Verification",
    "Administration Route Check",
    "Drug Interaction Review",
    "Patient Education",
    "Side Effect Monitoring",
    "Storage Requirements",
    "Expiry Date Verification"
  ],
  "Equipment Checklist": [
    "Equipment Functionality Test",
    "Safety Inspection",
    "Calibration Verification",
    "Maintenance Schedule Check",
    "User Training Verification",
    "Documentation Review",
    "Compliance Audit",
    "Emergency Procedures"
  ],
};

// Generate table data for checklists
const generateChecklistTableData = () => {
  const data: Array<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    units: string;
  }> = [];

  Object.entries(checklistTopics).forEach(([area, topics]) => {
    topics.forEach((topic, topicIndex) => {
      const units = checklistUnits[topic] || [];
      data.push({
        area,
        topic,
        topicId: `CHK-${area.substring(0, 3).toUpperCase()}-${String(topicIndex + 1).padStart(3, '0')}`,
        version: "v3.0",
        responsibleDept: area.includes("Patient") ? "Clinical Services" :
                        area.includes("Medication") ? "Pharmacy Services" : "Technical Services",
        contentOwner: area.includes("Patient") ? "Clinical Safety Team" :
                     area.includes("Medication") ? "Pharmacy Safety Team" : "Technical Safety Team",
        units: units.join(", "),
      });
    });
  });

  return data;
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export default function ChecklistLibraryAssignment() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedArea, setSelectedArea] = useState("");
  const [viewAssignmentOpen, setViewAssignmentOpen] = useState(false);
  const [selectedAssignmentItem, setSelectedAssignmentItem] = useState<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    units?: string;
  } | null>(null);

  const tableData = generateChecklistTableData();

  // Filter data based on search term and selected area
  const filteredData = tableData.filter((item) => {
    const matchesSearch = searchTerm === "" ||
      item.area.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.topic.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.units && item.units.toLowerCase().includes(searchTerm.toLowerCase())) ||
      item.topicId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.responsibleDept.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.contentOwner.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesArea = selectedArea === "" || item.area === selectedArea;

    return matchesSearch && matchesArea;
  });

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/employee-training-assignment')}
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Back to Employee Training Assignment
          </Button>
        </div>
        
        <h1 className="text-3xl font-bold tracking-tight mb-2">
          Checklist Library & Assignment
        </h1>
        <p className="text-muted-foreground">
          Browse and assign checklists from the library organized by checklist categories
        </p>
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="shadow-sm border-slate-200">
          <div className="p-6">
            {/* Search and Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search checklist areas, topics, or IDs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={selectedArea}
                onChange={(e) => setSelectedArea(e.target.value)}
                className="px-3 py-2 border border-input bg-background rounded-md text-sm"
              >
                <option value="">All Checklist Areas</option>
                {checklistAreas.map((area) => (
                  <option key={area} value={area}>
                    {area}
                  </option>
                ))}
              </select>

              <Button variant="outline" className="gap-2">
                <Download size={16} />
                Export
              </Button>
            </div>

            {/* Results Summary */}
            <div className="mb-4 text-sm text-muted-foreground">
              Showing {filteredData.length} of {tableData.length} checklists
              {searchTerm && ` for "${searchTerm}"`}
              {selectedArea && ` in ${selectedArea}`}
            </div>

            {/* Checklist Library Table */}
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader className="bg-slate-50">
                  <TableRow>
                    <TableHead className="font-medium">Area</TableHead>
                    <TableHead className="font-medium">Topic</TableHead>
                    <TableHead className="font-medium">Units</TableHead>
                    <TableHead className="font-medium">Topic ID</TableHead>
                    <TableHead className="font-medium">Version</TableHead>
                    <TableHead className="font-medium">Responsible Dept.</TableHead>
                    <TableHead className="font-medium">Content Owner</TableHead>
                    <TableHead className="font-medium text-center">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.length > 0 ? (
                    filteredData.map((item, index) => (
                      <TableRow key={index} className="hover:bg-muted/50">
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <CheckSquare size={16} className="text-orange-600" />
                            {item.area}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">{item.topic}</TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {item.units || "No units"}
                        </TableCell>
                        <TableCell className="font-mono text-sm">{item.topicId}</TableCell>
                        <TableCell>{item.version}</TableCell>
                        <TableCell>{item.responsibleDept}</TableCell>
                        <TableCell>{item.contentOwner}</TableCell>
                        <TableCell>
                          <div className="flex gap-2 justify-center">
                            <Button
                              size="sm"
                              className="gap-1 bg-purple-600 hover:bg-purple-700"
                              onClick={() => {
                                // Handle assign/passage functionality
                                console.log("Assign/Passage clicked for:", item);
                              }}
                            >
                              <Users size={14} />
                              Assign/Passage
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="gap-1"
                              onClick={() => {
                                setSelectedAssignmentItem(item);
                                setViewAssignmentOpen(true);
                              }}
                            >
                              <Eye size={14} />
                              View Assignment
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <Filter className="h-8 w-8 mb-2 opacity-40" />
                          <p className="text-sm font-medium">No checklists found</p>
                          <p className="text-xs mt-1">
                            Try adjusting your search or filter criteria
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* View Assignment Dialog */}
      <ViewAssignmentDialog
        open={viewAssignmentOpen}
        onOpenChange={setViewAssignmentOpen}
        assignmentItem={selectedAssignmentItem}
      />
    </motion.div>
  );
}
