import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { motion } from "framer-motion";
import {
  Search,
  ArrowLeft,
  Eye,
  Users,
  Filter,
  Download,
  FileText,
} from "lucide-react";
import { ViewAssignmentDialog } from "@/components/ViewAssignmentDialog";

// Document data structure - focused on documents from CFD hierarchy
const documentAreas = [
  "Patient Safety Documents",
  "Medication Documents", 
  "Equipment Documents",
];

const documentTopics = {
  "Patient Safety Documents": [
    "Patient Identification Document",
    "Medical History Document",
    "Initial Assessment Document",
    "Discharge Summary Document",
    "Follow-up Instructions Document",
    "Medication List Document",
    "Vital Signs Document",
    "Pain Assessment Document",
    "Progress Notes Document",
  ],
  "Medication Documents": [
    "Medication Order Document",
    "Dosage Calculation Document",
    "Contraindications Document",
    "Administration Schedule Document",
    "Patient Response Document",
    "Side Effects Document",
    "Stock Level Document",
    "Expiry Tracking Document",
    "Reorder Document",
  ],
  "Equipment Documents": [
    "Maintenance Schedule Document",
    "Service History Document",
    "Parts Replacement Document",
    "Calibration Procedure Document",
    "Accuracy Verification Document",
    "Calibration Log Document",
    "Safety Inspection Document",
    "Functionality Test Document",
    "Compliance Document",
  ],
};

const documentUnits = {
  "Patient Identification Document": ["ID Verification", "Wristband Check", "Photo Verification"],
  "Medical History Document": ["Past Medical History", "Family History", "Social History"],
  "Initial Assessment Document": ["Physical Assessment", "Mental Status", "Risk Assessment"],
  "Discharge Summary Document": ["Diagnosis Summary", "Treatment Summary", "Follow-up Plan"],
  "Follow-up Instructions Document": ["Medication Instructions", "Activity Restrictions", "Appointment Schedule"],
  "Medication List Document": ["Current Medications", "Allergies", "Discontinued Medications"],
  "Vital Signs Document": ["Temperature", "Blood Pressure", "Heart Rate", "Respiratory Rate"],
  "Pain Assessment Document": ["Pain Scale", "Pain Location", "Pain Management"],
  "Progress Notes Document": ["Daily Progress", "Treatment Response", "Plan Updates"],
  "Medication Order Document": ["Prescription Details", "Dosage Instructions", "Administration Route"],
  "Dosage Calculation Document": ["Weight-based Dosing", "Age-based Dosing", "Condition-based Dosing"],
  "Contraindications Document": ["Drug Allergies", "Medical Conditions", "Drug Interactions"],
  "Administration Schedule Document": ["Timing Schedule", "Frequency", "Duration"],
  "Patient Response Document": ["Therapeutic Response", "Adverse Reactions", "Compliance"],
  "Side Effects Document": ["Common Side Effects", "Serious Side Effects", "Monitoring Requirements"],
  "Stock Level Document": ["Current Inventory", "Minimum Levels", "Reorder Points"],
  "Expiry Tracking Document": ["Expiry Dates", "Rotation Schedule", "Disposal Procedures"],
  "Reorder Document": ["Supplier Information", "Order Quantities", "Delivery Schedule"],
  "Maintenance Schedule Document": ["Preventive Maintenance", "Routine Checks", "Service Intervals"],
  "Service History Document": ["Service Records", "Repair History", "Warranty Information"],
  "Parts Replacement Document": ["Replacement Schedule", "Parts Inventory", "Installation Procedures"],
  "Calibration Procedure Document": ["Calibration Steps", "Standards Used", "Frequency"],
  "Accuracy Verification Document": ["Test Procedures", "Acceptance Criteria", "Documentation"],
  "Calibration Log Document": ["Calibration Records", "Results", "Technician Notes"],
  "Safety Inspection Document": ["Safety Checks", "Hazard Assessment", "Compliance Verification"],
  "Functionality Test Document": ["Performance Tests", "Operational Checks", "Quality Assurance"],
  "Compliance Document": ["Regulatory Requirements", "Standards Compliance", "Audit Trail"],
};

// Generate table data for documents
const generateDocumentTableData = () => {
  const data: Array<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    units: string;
  }> = [];

  Object.entries(documentTopics).forEach(([area, topics]) => {
    topics.forEach((topic, topicIndex) => {
      const units = documentUnits[topic] || [];
      data.push({
        area,
        topic,
        topicId: `DOC-${area.substring(0, 3).toUpperCase()}-${String(topicIndex + 1).padStart(3, '0')}`,
        version: "v1.2",
        responsibleDept: area.includes("Patient") ? "Clinical Services" :
                        area.includes("Medication") ? "Pharmacy Services" : "Technical Services",
        contentOwner: area.includes("Patient") ? "Clinical Documentation Team" :
                     area.includes("Medication") ? "Pharmacy Documentation Team" : "Technical Documentation Team",
        units: units.join(", "),
      });
    });
  });

  return data;
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export default function DocLibraryAssignment() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedArea, setSelectedArea] = useState("");
  const [viewAssignmentOpen, setViewAssignmentOpen] = useState(false);
  const [selectedAssignmentItem, setSelectedAssignmentItem] = useState<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    units?: string;
  } | null>(null);

  const tableData = generateDocumentTableData();

  // Filter data based on search term and selected area
  const filteredData = tableData.filter((item) => {
    const matchesSearch = searchTerm === "" ||
      item.area.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.topic.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.units && item.units.toLowerCase().includes(searchTerm.toLowerCase())) ||
      item.topicId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.responsibleDept.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.contentOwner.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesArea = selectedArea === "" || item.area === selectedArea;

    return matchesSearch && matchesArea;
  });

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/employee-training-assignment')}
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Back to Employee Training Assignment
          </Button>
        </div>
        
        <h1 className="text-3xl font-bold tracking-tight mb-2">
          Doc Library & Assignment
        </h1>
        <p className="text-muted-foreground">
          Browse and assign documents from the library organized by document categories
        </p>
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="shadow-sm border-slate-200">
          <div className="p-6">
            {/* Search and Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search document areas, topics, or IDs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={selectedArea}
                onChange={(e) => setSelectedArea(e.target.value)}
                className="px-3 py-2 border border-input bg-background rounded-md text-sm"
              >
                <option value="">All Document Areas</option>
                {documentAreas.map((area) => (
                  <option key={area} value={area}>
                    {area}
                  </option>
                ))}
              </select>

              <Button variant="outline" className="gap-2">
                <Download size={16} />
                Export
              </Button>
            </div>

            {/* Results Summary */}
            <div className="mb-4 text-sm text-muted-foreground">
              Showing {filteredData.length} of {tableData.length} documents
              {searchTerm && ` for "${searchTerm}"`}
              {selectedArea && ` in ${selectedArea}`}
            </div>

            {/* Document Library Table */}
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader className="bg-slate-50">
                  <TableRow>
                    <TableHead className="font-medium">Area</TableHead>
                    <TableHead className="font-medium">Topic</TableHead>
                    <TableHead className="font-medium">Units</TableHead>
                    <TableHead className="font-medium">Topic ID</TableHead>
                    <TableHead className="font-medium">Version</TableHead>
                    <TableHead className="font-medium">Responsible Dept.</TableHead>
                    <TableHead className="font-medium">Content Owner</TableHead>
                    <TableHead className="font-medium text-center">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.length > 0 ? (
                    filteredData.map((item, index) => (
                      <TableRow key={index} className="hover:bg-muted/50">
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <FileText size={16} className="text-blue-600" />
                            {item.area}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">{item.topic}</TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {item.units || "No units"}
                        </TableCell>
                        <TableCell className="font-mono text-sm">{item.topicId}</TableCell>
                        <TableCell>{item.version}</TableCell>
                        <TableCell>{item.responsibleDept}</TableCell>
                        <TableCell>{item.contentOwner}</TableCell>
                        <TableCell>
                          <div className="flex gap-2 justify-center">
                            <Button
                              size="sm"
                              className="gap-1 bg-purple-600 hover:bg-purple-700"
                              onClick={() => {
                                // Handle assign/passage functionality
                                console.log("Assign/Passage clicked for:", item);
                              }}
                            >
                              <Users size={14} />
                              Assign/Passage
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="gap-1"
                              onClick={() => {
                                setSelectedAssignmentItem(item);
                                setViewAssignmentOpen(true);
                              }}
                            >
                              <Eye size={14} />
                              View Assignment
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <Filter className="h-8 w-8 mb-2 opacity-40" />
                          <p className="text-sm font-medium">No documents found</p>
                          <p className="text-xs mt-1">
                            Try adjusting your search or filter criteria
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* View Assignment Dialog */}
      <ViewAssignmentDialog
        open={viewAssignmentOpen}
        onOpenChange={setViewAssignmentOpen}
        assignmentItem={selectedAssignmentItem}
      />
    </motion.div>
  );
}
